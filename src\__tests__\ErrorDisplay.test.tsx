import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ErrorDisplay from '@/components/ErrorDisplay';

// Mock PrimeReact components
jest.mock('primereact/message', () => ({
  Message: ({ children, severity }: any) => (
    <div data-testid="message" data-severity={severity}>
      {children}
    </div>
  ),
}));

jest.mock('primereact/button', () => ({
  Button: ({ label, icon, onClick, tooltip }: any) => (
    <button onClick={onClick} title={tooltip} data-icon={icon}>
      {label}
    </button>
  ),
}));

describe('ErrorDisplay Component', () => {
  const mockOnRetry = jest.fn();
  const mockOnDismiss = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when error is null', () => {
    render(<ErrorDisplay error={null} />);
    expect(screen.queryByTestId('message')).not.toBeInTheDocument();
  });

  it('should not render when error is empty string', () => {
    render(<ErrorDisplay error="" />);
    expect(screen.queryByTestId('message')).not.toBeInTheDocument();
  });

  it('should render quota error with correct styling', () => {
    const quotaError = 'API 配額已用完，請稍後再試或檢查您的配額限制';
    render(<ErrorDisplay error={quotaError} />);

    expect(screen.getByTestId('message')).toHaveAttribute('data-severity', 'warn');
    expect(screen.getByText('API 配額限制')).toBeInTheDocument();
    expect(screen.getByText(quotaError)).toBeInTheDocument();
  });

  it('should render permission error with correct styling', () => {
    const permissionError = 'API Key 沒有使用此功能的權限';
    render(<ErrorDisplay error={permissionError} />);

    expect(screen.getByTestId('message')).toHaveAttribute('data-severity', 'error');
    expect(screen.getByText('API Key 問題')).toBeInTheDocument();
    expect(screen.getByText(permissionError)).toBeInTheDocument();
  });

  it('should render network error with correct styling', () => {
    const networkError = '網路連線錯誤，請檢查網路連線後再試';
    render(<ErrorDisplay error={networkError} />);

    expect(screen.getByTestId('message')).toHaveAttribute('data-severity', 'info');
    expect(screen.getByText('網路連線問題')).toBeInTheDocument();
    expect(screen.getByText(networkError)).toBeInTheDocument();
  });

  it('should render general error with default styling', () => {
    const generalError = '發生未知錯誤';
    render(<ErrorDisplay error={generalError} />);

    expect(screen.getByTestId('message')).toHaveAttribute('data-severity', 'error');
    expect(screen.getByText('發生錯誤')).toBeInTheDocument();
    expect(screen.getByText(generalError)).toBeInTheDocument();
  });

  it('should show retry button when onRetry is provided', () => {
    render(<ErrorDisplay error="Test error" onRetry={mockOnRetry} />);

    const retryButton = screen.getByText('重試');
    expect(retryButton).toBeInTheDocument();

    fireEvent.click(retryButton);
    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it('should show dismiss button when onDismiss is provided', () => {
    render(<ErrorDisplay error="Test error" onDismiss={mockOnDismiss} />);

    const dismissButton = screen.getByTitle('關閉');
    expect(dismissButton).toBeInTheDocument();

    fireEvent.click(dismissButton);
    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });

  it('should not show retry button when onRetry is not provided', () => {
    render(<ErrorDisplay error="Test error" />);
    expect(screen.queryByText('重試')).not.toBeInTheDocument();
  });

  it('should not show dismiss button when onDismiss is not provided', () => {
    render(<ErrorDisplay error="Test error" />);
    expect(screen.queryByTitle('關閉')).not.toBeInTheDocument();
  });

  it('should show quota error suggestions', () => {
    const quotaError = 'RESOURCE_EXHAUSTED';
    render(<ErrorDisplay error={quotaError} />);

    expect(screen.getByText(/請稍後再試.*配額會在一段時間後重置/)).toBeInTheDocument();
    expect(screen.getByText(/檢查您的 Google Cloud Console 配額設定/)).toBeInTheDocument();
    expect(screen.getByText(/考慮升級您的 API 使用方案/)).toBeInTheDocument();
  });

  it('should show permission error suggestions', () => {
    const permissionError = 'PERMISSION_DENIED';
    render(<ErrorDisplay error={permissionError} />);

    expect(screen.getByText(/檢查 API Key 是否正確輸入/)).toBeInTheDocument();
    expect(screen.getByText(/確認 API Key 具有圖片生成權限/)).toBeInTheDocument();
    expect(screen.getByText(/檢查 API Key 是否已過期/)).toBeInTheDocument();
  });

  it('should show network error suggestions', () => {
    const networkError = '網路連線';
    render(<ErrorDisplay error={networkError} />);

    expect(screen.getByText(/檢查網路連線是否正常/)).toBeInTheDocument();
    expect(screen.getByText(/嘗試重新整理頁面/)).toBeInTheDocument();
    expect(screen.getByText(/確認防火牆沒有阻擋連線/)).toBeInTheDocument();
  });

  it('should show general error suggestions', () => {
    const generalError = 'Some other error';
    render(<ErrorDisplay error={generalError} />);

    expect(screen.getByText(/請稍後再試/)).toBeInTheDocument();
    expect(screen.getByText(/如果問題持續發生，請檢查 API Key 設定/)).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(
      <ErrorDisplay error="Test error" className="custom-class" />
    );

    expect(container.firstChild).toHaveClass('error-display', 'custom-class');
  });
});
