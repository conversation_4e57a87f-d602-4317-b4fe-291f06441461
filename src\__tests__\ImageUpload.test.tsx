import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageUpload from '@/components/ImageUpload';

// Mock useReferenceImage hook
const mockSetReferenceImage = jest.fn();
const mockSetReferenceImagePreview = jest.fn();

jest.mock('@/hooks/useGemini', () => ({
  useReferenceImage: () => ({
    referenceImage: null,
    referenceImagePreview: null,
    setReferenceImage: mockSetReferenceImage,
    setReferenceImagePreview: mockSetReferenceImagePreview,
  }),
}));

// Mock Toast component
jest.mock('primereact/toast', () => ({
  Toast: ({ children }: any) => <div data-testid="toast">{children}</div>,
}));

describe('ImageUpload Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render upload area when no image is selected', () => {
    render(<ImageUpload />);
    
    expect(screen.getByText('選擇或拖拉圖片到此處')).toBeInTheDocument();
    expect(screen.getByText('支援 JPG、PNG、GIF 等格式，最大 10MB')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '選擇檔案' })).toBeInTheDocument();
  });

  it('should show paste instruction', () => {
    render(<ImageUpload />);
    
    expect(screen.getByText(/您也可以直接.*Ctrl\+V.*貼上剪貼簿中的圖片/)).toBeInTheDocument();
  });

  it('should show usage instructions', () => {
    render(<ImageUpload />);
    
    expect(screen.getByText(/📁.*檔案選擇：.*點擊「選擇檔案」按鈕/)).toBeInTheDocument();
    expect(screen.getByText(/🖱️.*拖拉上傳：.*將圖片檔案拖拉到上方區域/)).toBeInTheDocument();
    expect(screen.getByText(/📋.*複製貼上：.*複製圖片後按 Ctrl\+V.*貼上/)).toBeInTheDocument();
  });

  it('should handle drag over events', () => {
    render(<ImageUpload />);
    
    const dropZone = screen.getByText('選擇或拖拉圖片到此處').closest('div');
    
    // Test drag enter
    fireEvent.dragEnter(dropZone!, {
      dataTransfer: {
        files: [new File(['test'], 'test.jpg', { type: 'image/jpeg' })],
      },
    });
    
    expect(screen.getByText('放開以上傳圖片')).toBeInTheDocument();
  });

  it('should handle file drop', async () => {
    render(<ImageUpload />);
    
    const dropZone = screen.getByText('選擇或拖拉圖片到此處').closest('div');
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    fireEvent.drop(dropZone!, {
      dataTransfer: {
        files: [file],
      },
    });
    
    await waitFor(() => {
      expect(mockSetReferenceImage).toHaveBeenCalledWith(file);
    });
  });

  it('should reject non-image files', async () => {
    render(<ImageUpload />);
    
    const dropZone = screen.getByText('選擇或拖拉圖片到此處').closest('div');
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    fireEvent.drop(dropZone!, {
      dataTransfer: {
        files: [file],
      },
    });
    
    // Should not call setReferenceImage for non-image files
    expect(mockSetReferenceImage).not.toHaveBeenCalled();
  });

  it('should handle file input change', async () => {
    render(<ImageUpload />);
    
    const fileInput = screen.getByRole('button', { name: '選擇檔案' })
      .closest('div')?.querySelector('input[type="file"]') as HTMLInputElement;
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    });
    
    fireEvent.change(fileInput);
    
    await waitFor(() => {
      expect(mockSetReferenceImage).toHaveBeenCalledWith(file);
    });
  });
});

// Test with selected image
describe('ImageUpload Component with selected image', () => {
  const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock with selected image
    jest.mocked(require('@/hooks/useGemini').useReferenceImage).mockReturnValue({
      referenceImage: mockFile,
      referenceImagePreview: 'data:image/jpeg;base64,test',
      setReferenceImage: mockSetReferenceImage,
      setReferenceImagePreview: mockSetReferenceImagePreview,
    });
  });

  it('should render image preview when image is selected', () => {
    render(<ImageUpload />);
    
    expect(screen.getByText('已選擇參考圖片')).toBeInTheDocument();
    expect(screen.getByText('test.jpg')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '移除圖片' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: '重新選擇' })).toBeInTheDocument();
  });

  it('should handle remove image', () => {
    render(<ImageUpload />);
    
    const removeButton = screen.getByRole('button', { name: '移除圖片' });
    fireEvent.click(removeButton);
    
    expect(mockSetReferenceImage).toHaveBeenCalledWith(null);
    expect(mockSetReferenceImagePreview).toHaveBeenCalledWith(null);
  });
});
