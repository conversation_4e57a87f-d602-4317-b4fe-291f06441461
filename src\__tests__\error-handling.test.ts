import { GeminiAPIError } from '@/utils/gemini-api';

describe('GeminiAPIError', () => {
  it('should create error with correct properties', () => {
    const errorResponse = {
      code: 429,
      message: 'Resource has been exhausted (e.g. check quota).',
      status: 'RESOURCE_EXHAUSTED'
    };

    const error = new GeminiAPIError(errorResponse);

    expect(error.name).toBe('GeminiAPIError');
    expect(error.code).toBe(429);
    expect(error.status).toBe('RESOURCE_EXHAUSTED');
    expect(error.message).toBe('Resource has been exhausted (e.g. check quota).');
  });

  describe('getLocalizedMessage', () => {
    it('should return localized message for 400 INVALID_ARGUMENT', () => {
      const error = new GeminiAPIError({
        code: 400,
        message: 'Invalid argument',
        status: 'INVALID_ARGUMENT'
      });

      expect(error.getLocalizedMessage()).toBe('請求參數無效，請檢查提示詞或參考圖片格式');
    });

    it('should return localized message for 401 error', () => {
      const error = new GeminiAPIError({
        code: 401,
        message: 'Unauthorized',
        status: 'UNAUTHENTICATED'
      });

      expect(error.getLocalizedMessage()).toBe('API Key 無效或已過期，請重新設定 API Key');
    });

    it('should return localized message for 403 PERMISSION_DENIED', () => {
      const error = new GeminiAPIError({
        code: 403,
        message: 'Permission denied',
        status: 'PERMISSION_DENIED'
      });

      expect(error.getLocalizedMessage()).toBe('API Key 沒有使用此功能的權限，請檢查 API Key 設定');
    });

    it('should return localized message for 404 error', () => {
      const error = new GeminiAPIError({
        code: 404,
        message: 'Not found',
        status: 'NOT_FOUND'
      });

      expect(error.getLocalizedMessage()).toBe('找不到指定的模型，請選擇其他可用模型');
    });

    it('should return localized message for 429 RESOURCE_EXHAUSTED', () => {
      const error = new GeminiAPIError({
        code: 429,
        message: 'Resource has been exhausted (e.g. check quota).',
        status: 'RESOURCE_EXHAUSTED'
      });

      expect(error.getLocalizedMessage()).toBe('API 配額已用完，請稍後再試或檢查您的配額限制');
    });

    it('should return localized message for 429 rate limit', () => {
      const error = new GeminiAPIError({
        code: 429,
        message: 'Too many requests',
        status: 'TOO_MANY_REQUESTS'
      });

      expect(error.getLocalizedMessage()).toBe('請求過於頻繁，請稍後再試');
    });

    it('should return localized message for 500 error', () => {
      const error = new GeminiAPIError({
        code: 500,
        message: 'Internal server error',
        status: 'INTERNAL'
      });

      expect(error.getLocalizedMessage()).toBe('伺服器內部錯誤，請稍後再試');
    });

    it('should return localized message for 503 error', () => {
      const error = new GeminiAPIError({
        code: 503,
        message: 'Service unavailable',
        status: 'UNAVAILABLE'
      });

      expect(error.getLocalizedMessage()).toBe('服務暫時無法使用，請稍後再試');
    });

    it('should return default message for unknown error code', () => {
      const error = new GeminiAPIError({
        code: 999,
        message: 'Unknown error',
        status: 'UNKNOWN'
      });

      expect(error.getLocalizedMessage()).toBe('Unknown error');
    });

    it('should return default message when no message provided', () => {
      const error = new GeminiAPIError({
        code: 999,
        message: '',
        status: 'UNKNOWN'
      });

      expect(error.getLocalizedMessage()).toBe('發生未知錯誤，請稍後再試');
    });
  });
});
