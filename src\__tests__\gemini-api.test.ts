import { validateApiKey, fileToBase64, combinePrompts, generateImage, downloadImage } from '@/utils/gemini-api'

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>

describe('Gemini API Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('validateApiKey', () => {
    it('should return true for valid API key', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
      } as Response)

      const result = await validateApiKey('valid-api-key')
      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'https://generativelanguage.googleapis.com/v1beta/models?key=valid-api-key',
        expect.objectContaining({
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      )
    })

    it('should return false for invalid API key', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
      } as Response)

      const result = await validateApi<PERSON>ey('invalid-api-key')
      expect(result).toBe(false)
    })

    it('should return false when fetch throws error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await validateApiKey('api-key')
      expect(result).toBe(false)
    })
  })

  describe('fileToBase64', () => {
    it('should convert file to base64', async () => {
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })

      const result = await fileToBase64(mockFile)
      expect(result).toBe('/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=')
    })
  })

  describe('combinePrompts', () => {
    it('should combine prompts successfully', async () => {
      const mockResponse = {
        candidates: [
          {
            content: {
              parts: [
                {
                  text: '一個穿著牛仔褲、戴著黃色帽子的可愛公仔，背景是城市夜景'
                }
              ]
            }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response)

      const result = await combinePrompts(
        'api-key',
        '一個可愛的公仔，背景是城市夜景',
        '穿著牛仔褲，戴著黃色的帽子'
      )

      expect(result).toBe('一個穿著牛仔褲、戴著黃色帽子的可愛公仔，背景是城市夜景')
      expect(mockFetch).toHaveBeenCalledWith(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=api-key',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        })
      )
    })

    it('should throw error when API response is not ok', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
      } as Response)

      await expect(combinePrompts('api-key', 'base', 'refinement')).rejects.toThrow('API 請求失敗: 400 Bad Request')
    })
  })

  describe('generateImage', () => {
    it('should generate image successfully', async () => {
      const mockResponse = {
        candidates: [
          {
            content: {
              parts: [
                {
                  inlineData: {
                    mimeType: 'image/png',
                    data: 'base64-image-data'
                  }
                }
              ]
            }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response)

      const result = await generateImage('api-key', 'test prompt', 'gemini-2.5-flash-image-preview')
      expect(result).toBe('data:image/png;base64,base64-image-data')
    })

    it('should generate image with reference image', async () => {
      const mockResponse = {
        candidates: [
          {
            content: {
              parts: [
                {
                  inlineData: {
                    mimeType: 'image/png',
                    data: 'base64-image-data'
                  }
                }
              ]
            }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response)

      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      const result = await generateImage('api-key', 'test prompt', 'gemini-2.5-flash-image-preview', mockFile)
      expect(result).toBe('data:image/png;base64,base64-image-data')
    })

    it('should use correct response modalities for new model', async () => {
      const mockResponse = {
        candidates: [
          {
            content: {
              parts: [
                {
                  inlineData: {
                    mimeType: 'image/png',
                    data: 'base64-image-data'
                  }
                }
              ]
            }
          }
        ]
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      } as Response)

      await generateImage('api-key', 'test prompt', 'gemini-2.0-flash-preview-image-generation')

      // 檢查請求是否包含正確的 responseModalities
      const requestBody = JSON.parse(mockFetch.mock.calls[0][1]?.body as string)
      expect(requestBody.generationConfig).toBeDefined()
      expect(requestBody.generationConfig.responseModalities).toEqual(['IMAGE', 'TEXT'])
    })
  })

  describe('downloadImage', () => {
    it('should create download link and trigger download', () => {
      const mockLink = {
        href: '',
        download: '',
        click: jest.fn(),
      }

      document.createElement = jest.fn().mockReturnValue(mockLink)

      downloadImage('data:image/png;base64,test', 'test.png')

      expect(document.createElement).toHaveBeenCalledWith('a')
      expect(mockLink.href).toBe('data:image/png;base64,test')
      expect(mockLink.download).toBe('test.png')
      expect(mockLink.click).toHaveBeenCalled()
      expect(document.body.appendChild).toHaveBeenCalledWith(mockLink)
      expect(document.body.removeChild).toHaveBeenCalledWith(mockLink)
    })
  })
})
