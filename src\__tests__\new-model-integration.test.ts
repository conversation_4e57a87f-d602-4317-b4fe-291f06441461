// 新模型整合測試
import { GEMINI_MODELS } from '@/hooks/useGemini';

describe('New Model Integration', () => {
  test('should include new model in GEMINI_MODELS', () => {
    const newModel = GEMINI_MODELS.find(
      model => model.value === 'gemini-2.0-flash-preview-image-generation'
    );
    
    expect(newModel).toBeDefined();
    expect(newModel?.label).toBe('Gemini 2.0 Flash Preview Image Generation');
    expect(newModel?.type).toBe('image');
  });

  test('should have new model as first in the list', () => {
    expect(GEMINI_MODELS[0].value).toBe('gemini-2.0-flash-preview-image-generation');
  });

  test('should have correct number of models', () => {
    expect(GEMINI_MODELS).toHaveLength(4);
  });

  test('should have correct number of image models', () => {
    const imageModels = GEMINI_MODELS.filter(model => model.type === 'image');
    expect(imageModels).toHaveLength(3);
  });

  test('should maintain existing models', () => {
    const modelValues = GEMINI_MODELS.map(model => model.value);
    
    expect(modelValues).toContain('gemini-2.5-flash-image-preview');
    expect(modelValues).toContain('imagen-3.0-generate-002');
    expect(modelValues).toContain('gemini-2.0-flash');
  });
});
