import {
  DEFAULT_PROMPTS,
  searchPrompts
} from '@/data/prompts';

describe('Prompts Data', () => {
  describe('DEFAULT_PROMPTS', () => {
    it('should contain expected prompts', () => {
      expect(DEFAULT_PROMPTS.length).toBeGreaterThan(0);

      // 檢查是否包含來自 prompts.md 的提示詞
      const computerDeskPrompt = DEFAULT_PROMPTS.find(p => p.id === 'computer-desk-figurine');
      expect(computerDeskPrompt).toBeDefined();
      expect(computerDeskPrompt?.name).toBe('電腦桌公仔');

      const ghibliPrompt = DEFAULT_PROMPTS.find(p => p.id === 'ghibli-style');
      expect(ghibliPrompt).toBeDefined();
      expect(ghibliPrompt?.name).toBe('吉卜力風格');
    });

    it('should have valid structure for each prompt', () => {
      DEFAULT_PROMPTS.forEach(prompt => {
        expect(prompt.id).toBeDefined();
        expect(prompt.name).toBeDefined();
        expect(prompt.description).toBeDefined();
        expect(prompt.prompt).toBeDefined();
        expect(prompt.icon).toBeDefined();
        expect(typeof prompt.icon).toBe('string');
        expect(prompt.icon).toMatch(/^pi-/); // 檢查圖示格式
      });
    });
  });



  describe('searchPrompts', () => {
    it('should find prompts by name', () => {
      const results = searchPrompts('電腦桌');
      expect(results.length).toBeGreaterThan(0);

      const foundPrompt = results.find(p => p.id === 'computer-desk-figurine');
      expect(foundPrompt).toBeDefined();
    });

    it('should find prompts by description', () => {
      const results = searchPrompts('吉卜力');
      expect(results.length).toBeGreaterThan(0);
    });

    it('should be case insensitive', () => {
      const results1 = searchPrompts('電腦桌');
      const results2 = searchPrompts('電腦桌');
      expect(results1).toEqual(results2);
    });
  });
});
