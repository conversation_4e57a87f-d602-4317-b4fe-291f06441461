import { act } from '@testing-library/react'
import { useGeminiStore, GEMINI_MODELS } from '@/hooks/useGemini'

describe('useGemini Store', () => {
  beforeEach(() => {
    // Reset store state before each test
    useGeminiStore.getState().clearAll()
    useGeminiStore.getState().setApiKey('')
    useGeminiStore.getState().setApiKeyValid(false)
    useGeminiStore.getState().setSelectedModel('gemini-2.0-flash-preview-image-generation')
  })

  describe('API Key management', () => {
    it('should manage API key state', () => {
      const store = useGeminiStore.getState()

      expect(store.apiKey).toBe('')
      expect(store.isApiKeyValid).toBe(false)

      act(() => {
        store.setApiKey('test-api-key')
        store.setApiKeyValid(true)
      })

      expect(useGeminiStore.getState().apiKey).toBe('test-api-key')
      expect(useGeminiStore.getState().isApiKeyValid).toBe(true)
    })
  })

  describe('Model selection', () => {
    it('should manage model selection state', () => {
      const store = useGeminiStore.getState()

      expect(store.selectedModel).toBe('gemini-2.0-flash-preview-image-generation')

      act(() => {
        store.setSelectedModel('imagen-3.0-generate-002')
      })

      expect(useGeminiStore.getState().selectedModel).toBe('imagen-3.0-generate-002')
    })
  })

  describe('GEMINI_MODELS', () => {
    it('should contain expected models', () => {
      expect(GEMINI_MODELS).toHaveLength(4)

      const modelValues = GEMINI_MODELS.map(model => model.value)
      expect(modelValues).toContain('gemini-2.0-flash-preview-image-generation')
      expect(modelValues).toContain('gemini-2.5-flash-image-preview')
      expect(modelValues).toContain('imagen-3.0-generate-002')
      expect(modelValues).toContain('gemini-2.0-flash')

      const imageModels = GEMINI_MODELS.filter(model => model.type === 'image')
      expect(imageModels).toHaveLength(3)

      const textModels = GEMINI_MODELS.filter(model => model.type === 'text')
      expect(textModels).toHaveLength(1)
    })
  })

  describe('Store actions', () => {
    it('should handle image generation state', () => {
      const store = useGeminiStore.getState()

      expect(store.isGenerating).toBe(false)
      expect(store.generatedImage).toBe(null)

      act(() => {
        store.setIsGenerating(true)
        store.setGeneratedImage('data:image/png;base64,test')
      })

      expect(useGeminiStore.getState().isGenerating).toBe(true)
      expect(useGeminiStore.getState().generatedImage).toBe('data:image/png;base64,test')
    })

    it('should handle prompts state', () => {
      const store = useGeminiStore.getState()

      expect(store.prompt).toBe('')
      expect(store.refinementPrompt).toBe('')

      act(() => {
        store.setPrompt('Test prompt')
        store.setRefinementPrompt('Test refinement')
      })

      expect(useGeminiStore.getState().prompt).toBe('Test prompt')
      expect(useGeminiStore.getState().refinementPrompt).toBe('Test refinement')
    })

    it('should handle reference image state', () => {
      const store = useGeminiStore.getState()
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })

      expect(store.referenceImage).toBe(null)
      expect(store.referenceImagePreview).toBe(null)

      act(() => {
        store.setReferenceImage(mockFile)
        store.setReferenceImagePreview('data:image/jpeg;base64,test')
      })

      expect(useGeminiStore.getState().referenceImage).toBe(mockFile)
      expect(useGeminiStore.getState().referenceImagePreview).toBe('data:image/jpeg;base64,test')
    })

    it('should handle error state', () => {
      const store = useGeminiStore.getState()

      expect(store.error).toBe(null)

      act(() => {
        store.setError('Test error message')
      })

      expect(useGeminiStore.getState().error).toBe('Test error message')

      act(() => {
        store.setError(null)
      })

      expect(useGeminiStore.getState().error).toBe(null)
    })

    it('should clear all state', () => {
      const store = useGeminiStore.getState()

      // Set some state
      act(() => {
        store.setPrompt('Test prompt')
        store.setRefinementPrompt('Test refinement')
        store.setGeneratedImage('data:image/png;base64,test')
        store.setError('Test error')
        store.setIsGenerating(true)
      })

      // Clear all
      act(() => {
        store.clearAll()
      })

      const state = useGeminiStore.getState()
      expect(state.prompt).toBe('')
      expect(state.refinementPrompt).toBe('')
      expect(state.generatedImage).toBe(null)
      expect(state.error).toBe(null)
      expect(state.isGenerating).toBe(false)
      expect(state.referenceImage).toBe(null)
      expect(state.referenceImagePreview).toBe(null)
    })


  })
})
